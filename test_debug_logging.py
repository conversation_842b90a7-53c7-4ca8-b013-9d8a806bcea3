#!/usr/bin/env python3
"""
Test script to verify debug logging for chart recommendations.
"""

import os
import sys
import pandas as pd

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.logging_config import configure_logging
from app.services.chart_recommender import ChartData
from app.services.data_processor import DataProcessor

# Configure logging
logger = configure_logging()

def test_chart_recommendations_debug():
    """Test chart recommendations with debug logging enabled."""
    
    print("=== Testing Chart Recommendations Debug Logging ===")
    print(f"LOG_LEVEL from environment: {os.environ.get('LOG_LEVEL', 'Not set')}")
    print(f"VERBOSE_LOGGING from environment: {os.environ.get('VERBOSE_LOGGING', 'Not set')}")
    
    # Create sample data for testing
    sample_data = {
        'Category': ['A', 'B', 'C', 'D', 'E', 'A', 'B', 'C'],
        'Region': ['North', 'South', 'East', 'West', 'North', 'South', 'East', 'West'],
        'Sales': [100, 200, 150, 300, 120, 180, 160, 250],
        'Profit': [20, 40, 30, 60, 25, 35, 32, 50],
        'Date': ['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', 
                '2023-01-05', '2023-01-06', '2023-01-07', '2023-01-08']
    }
    
    df = pd.DataFrame(sample_data)
    
    # Process the data to get metadata
    processor = DataProcessor()
    processed_result = processor.process_data(df)
    metadata = processed_result['metadata']
    
    print(f"\nDataset metadata:")
    print(f"- Rows: {len(df)}")
    print(f"- Categorical columns: {metadata.get('categorical_columns', [])}")
    print(f"- Numerical columns: {metadata.get('numerical_columns', [])}")
    print(f"- Unique counts: {metadata.get('unique_counts', {})}")
    
    # Get chart recommendations
    chart_data = ChartData()
    recommendations = chart_data.get_rule_based_recommendations(metadata)
    
    print(f"\nGenerated {len(recommendations)} recommendations")
    
    # Show first few recommendations
    for i, rec in enumerate(recommendations[:3]):
        print(f"[{i+1}] {rec.get('chart_type', 'unknown')} - "
              f"X: {rec.get('fields', {}).get('x', 'N/A')}, "
              f"Y: {rec.get('fields', {}).get('numeric', 'N/A')} "
              f"({rec.get('fields', {}).get('aggregation', 'N/A')})")

if __name__ == "__main__":
    test_chart_recommendations_debug()
