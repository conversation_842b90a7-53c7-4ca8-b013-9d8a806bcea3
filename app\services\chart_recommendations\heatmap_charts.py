"""
Heatmap chart recommendations module.

This module contains functions for recommending heatmap charts.
"""

from typing import List, Dict, Optional, Tuple
from logging import getLogger
from app.core.logging_config import configure_logging
from app.services.chart_recommendations.base import ChartRecommendationBase

logger = configure_logging()

class HeatmapChartRecommender(ChartRecommendationBase):
    """Class for heatmap chart recommendations."""
    
    def get_recommendations(self, metadata: Dict) -> List[Dict]:
        """
        Get heatmap chart recommendations based on data characteristics.

        Args:
            metadata: The metadata dictionary containing information about the dataset

        Returns:
            List of heatmap chart recommendations
        """
        recommendations = []

        try:
            # Safely get columns from metadata with defaults
            categorical_columns = metadata.get("categorical_columns", [])
            unique_counts = metadata.get("unique_counts", {})
        
            # Prioritize categorical columns by unique count
            prioritized_categorical = []
            if categorical_columns:
                # Get unique counts for each categorical column
                cat_unique_counts = {col: unique_counts.get(col, 0) for col in categorical_columns}

                # Filter out columns with only one unique value
                valid_categorical = [col for col in categorical_columns if cat_unique_counts.get(col, 0) > 1]

                # Sort by unique count (ascending)
                prioritized_categorical = sorted(valid_categorical, key=lambda col: cat_unique_counts.get(col, float('inf')))
        
            # Categorical pair analysis for heatmaps (prioritized)
            if len(prioritized_categorical) >= 2:
                # Generate all valid combinations of categorical columns for heatmaps
                valid_heatmap_combinations = []

                for i, cat_col1 in enumerate(prioritized_categorical[:4]):  # Consider top 4 columns
                    for cat_col2 in prioritized_categorical[i+1:4]:  # Limit to first 4 columns
                        # Get unique counts for both columns
                        unique_count1 = unique_counts.get(cat_col1, 0)
                        unique_count2 = unique_counts.get(cat_col2, 0)
                        combined_cardinality = unique_count1 * unique_count2

                        # Only consider combinations if both columns have at least 2 unique values
                        # and the combined cardinality is reasonable
                        if unique_count1 >= 2 and unique_count2 >= 2 and combined_cardinality <= 400 and combined_cardinality >= 4:
                            valid_heatmap_combinations.append((cat_col1, cat_col2, combined_cardinality))

                # Sort combinations by combined cardinality (lower is better for visualization)
                valid_heatmap_combinations.sort(key=lambda x: x[2])

                # Log the combinations we're using
                if valid_heatmap_combinations:
                    logger.info(f"Valid heatmap combinations: {[(x[0], x[1]) for x in valid_heatmap_combinations]}")

                    # Use up to 3 different combinations for heatmaps
                    for i, (cat_col1, cat_col2, _) in enumerate(valid_heatmap_combinations[:3]):
                        recommendations.append({
                            "chart_type": "heatmap",
                            "fields": {
                                "x": cat_col1,
                                "x_type": "categorical",
                                "y": cat_col2,
                                "y_type": "categorical",
                                "numeric": "count",
                                "chart_title": f"Relationship between {cat_col1} and {cat_col2}"
                            }
                        })
        
            return recommendations

        except Exception as e:
            logger.error(f"Error in HeatmapChartRecommender.get_recommendations(): {str(e)}", exc_info=True)
            return []
    
    def _analyze_categorical_pair(
        self, col1: str, col2: str, unique_counts: Dict
    ) -> List[Dict]:
        """
        Analyze relationships between categorical columns.
        
        Args:
            col1: First categorical column
            col2: Second categorical column
            unique_counts: Dictionary of unique counts for each column
            
        Returns:
            List of heatmap recommendations
        """
        recommendations = []
        combined_cardinality = unique_counts.get(col1, 0) * unique_counts.get(col2, 0)
        
        if combined_cardinality <= 400:  # Reasonable size for heatmap
            recommendations.append({
                "chart_type": "heatmap",
                "fields": {"x": col1, "y": col2},
                "options": {
                    "aggregation": "count",
                    "normalize": "percentage"
                }
            })
        
        return recommendations
