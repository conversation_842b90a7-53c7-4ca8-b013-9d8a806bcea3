"""
Multi-group chart recommendations module.

This module contains functions for recommending multi-group charts.
"""

from typing import List, Dict, Optional, Tuple
from logging import getLogger
from app.core.logging_config import configure_logging
from app.services.chart_recommendations.base import ChartRecommendationBase

logger = configure_logging()

class MultiGroupChartRecommender(ChartRecommendationBase):
    """Class for multi-group chart recommendations."""

    def get_recommendations(self, metadata: Dict) -> List[Dict]:
        """
        Get multi-group chart recommendations based on data characteristics.

        Args:
            metadata: The metadata dictionary containing information about the dataset

        Returns:
            List of multi-group chart recommendations
        """
        recommendations = []

        try:
            # Safely get columns from metadata with defaults
            categorical_columns = metadata.get("categorical_columns", [])
            numerical_columns = metadata.get("numerical_columns", [])
            unique_counts = metadata.get("unique_counts", {})

            # Initialize used_combinations at the beginning to avoid scope issues
            used_combinations = set()

            # Prioritize categorical columns by unique count
            prioritized_categorical = []
            if categorical_columns:
                # Get unique counts for each categorical column
                cat_unique_counts = {col: unique_counts.get(col, 0) for col in categorical_columns}

                # Filter out columns with only one unique value
                valid_categorical = [col for col in categorical_columns if cat_unique_counts.get(col, 0) > 1]

                # Sort by unique count (ascending)
                prioritized_categorical = sorted(valid_categorical, key=lambda col: cat_unique_counts.get(col, float('inf')))

            # Multi-group charts (prioritized)
            if len(prioritized_categorical) >= 2 and numerical_columns:
                # Find valid combinations of categorical columns for multi-group charts
                valid_multigroup_combinations = []

                for i, cat_col1 in enumerate(prioritized_categorical[:4]):  # Consider top 4 columns
                    for cat_col2 in prioritized_categorical[i+1:4]:  # Limit to first 4 columns
                        # Get unique counts for both columns
                        unique_count1 = unique_counts.get(cat_col1, 0)
                        unique_count2 = unique_counts.get(cat_col2, 0)
                        combined_cardinality = unique_count1 * unique_count2

                        # Only consider combinations if both columns have at least 2 unique values
                        # and the combined cardinality is reasonable
                        if unique_count1 >= 2 and unique_count2 >= 2 and combined_cardinality <= 100:
                            valid_multigroup_combinations.append((cat_col1, cat_col2))

                # Log the combinations we're using
                if valid_multigroup_combinations:
                    logger.info(f"Valid multi-group combinations: {valid_multigroup_combinations}")

                # Use different combinations for each numerical column when possible
                if valid_multigroup_combinations:
                    for i, num_col in enumerate(numerical_columns[:2]):  # Limit to first 2 numerical columns
                        if i < len(valid_multigroup_combinations):
                            x_col, group_col = valid_multigroup_combinations[i]
                        else:
                            x_col, group_col = valid_multigroup_combinations[0]

                        # Create a unique combination key
                        combination_key = (x_col, group_col, num_col)

                        # Only add if this combination hasn't been used
                        if combination_key not in used_combinations:
                            used_combinations.add(combination_key)

                            # Check if x_col is a datetime column
                            is_datetime = x_col in metadata.get("datetime_columns", [])

                            # Add grouped bar chart
                            chart_rec = {
                                "chart_type": "grouped_bar",
                                "fields": {
                                    "x": x_col,
                                    "x_type": "datetime" if is_datetime else "categorical",
                                    "group": group_col,
                                    "numeric": num_col if num_col != "count" else "count",
                                    "chart_title": f"{num_col} by {x_col} and {group_col}"
                                }
                            }

                            # Add time_agg parameter for datetime columns
                            if is_datetime:
                                # Check if we should use quarterly aggregation
                                date_metadata = metadata.get("datetime_metadata", {}).get(x_col, {})
                                recommended_agg = self._get_recommended_time_aggregation(date_metadata)
                                chart_rec["fields"]["time_agg"] = recommended_agg

                            recommendations.append(chart_rec)
                            logger.info(f"Added grouped_bar chart with combination: {x_col} + {group_col} + {num_col}")

            # Handle high cardinality columns (only if not already covered)
            for cat_col in prioritized_categorical:
                cardinality = unique_counts.get(cat_col, 0)
                if cardinality <= self.ADVANCED_THRESHOLDS["grouped_bar"] and cardinality > 5:
                    # Find a suitable grouping field
                    grouping_field = self._suggest_grouping_field(cat_col, prioritized_categorical)
                    if grouping_field:
                        # Check if this combination is already used
                        num_col = numerical_columns[0] if numerical_columns else "count"
                        combination_key = (cat_col, grouping_field, num_col)

                        # Only add if this combination hasn't been used and is different from previous ones
                        if combination_key not in used_combinations:
                            used_combinations.add(combination_key)

                            # Check if cat_col is a datetime column
                            is_datetime = cat_col in metadata.get("datetime_columns", [])

                            # Create chart recommendation
                            chart_rec = {
                                "chart_type": "grouped_bar",
                                "fields": {
                                    "x": cat_col,
                                    "x_type": "datetime" if is_datetime else "categorical",
                                    "group": grouping_field,
                                    "numeric": num_col,
                                    "chart_title": f"{cat_col} grouped by {grouping_field}"
                                },
                                "options": {
                                    "sort": "value",
                                    "orientation": "vertical"
                                }
                            }

                            # Add time_agg parameter for datetime columns
                            if is_datetime:
                                # Check if we should use quarterly aggregation
                                date_metadata = metadata.get("datetime_metadata", {}).get(cat_col, {})
                                recommended_agg = self._get_recommended_time_aggregation(date_metadata)
                                chart_rec["fields"]["time_agg"] = recommended_agg

                            recommendations.append(chart_rec)
                            logger.info(f"Added high-cardinality grouped_bar chart with combination: {cat_col} + {grouping_field} + {num_col}")

            return recommendations

        except Exception as e:
            logger.error(f"Error in MultiGroupChartRecommender.get_recommendations(): {str(e)}", exc_info=True)
            return []

    def _suggest_grouping_field(self, column: str, categorical_columns: List[str]) -> Optional[str]:
        """
        Suggest a field for grouping based on column name patterns or other categorical columns.

        Args:
            column: The column to find a grouping field for
            categorical_columns: List of available categorical columns

        Returns:
            A suitable grouping field or None if none found
        """
        # Common patterns that indicate a grouping relationship
        common_group_patterns = ['category', 'type', 'group', 'class', 'segment', 'department', 'region']

        # First try to find a column that contains one of the common patterns
        for pattern in common_group_patterns:
            for col in categorical_columns:
                if col != column and pattern in col.lower():
                    return col

        # If no pattern match, return the first different categorical column
        for col in categorical_columns:
            if col != column:
                return col

        return None
