"""
Chart Data Service.

This module provides functionality for processing chart data and recommendations.
"""

import pandas as pd
from typing import List, Dict
from logging import getLogger
from app.core.logging_config import configure_logging
from app.services.data_aggregator.base import DataAggregator
from app.services.chart_recommender import get_allowed_chart_types_for_chart_type

logger = configure_logging()

class ChartData:
    """
    Chart Data service for processing chart recommendations and generating chart data.
    """
    
    def __init__(self):
        """Initialize the chart data service."""
        self.data_aggregator = DataAggregator()
        pass
    
    def process_chart_recommendations(self, df: pd.DataFrame, recommended_charts: List[Dict]) -> List[Dict]:
        """
        Process chart recommendations and generate chart data.

        Args:
            df: The pandas DataFrame containing the data
            recommended_charts: List of chart recommendations
            data_aggregator: The DataAggregator instance to use for chart data preparation

        Returns:
            List of processed chart data objects
        """
        chart_data = []

        for chart_rec in recommended_charts:
            if not self.is_valid_chart_recommendation(chart_rec):
                continue

            try:
                processed_chart = self.data_aggregator.prepare_chart_data(
                    df=df,
                    chart_type=chart_rec['chart_type'],
                    fields=chart_rec['fields']
                )
                if processed_chart:
                    # Add chart_group field to the processed chart data
                    if 'chart_group' in chart_rec:
                        processed_chart['chart_group'] = chart_rec['chart_group']

                    # Add allowed_chart_types field for UI chart type switching
                    chart_type = chart_rec['chart_type']
                    allowed_types = get_allowed_chart_types_for_chart_type(chart_type)
                    processed_chart['allowed_chart_types'] = allowed_types

                    chart_data.append(processed_chart)
                    logger.debug(f"Added chart {chart_type} with allowed types: {allowed_types}")
            except Exception as e:
                logger.error(f"Error processing chart {chart_rec['chart_type']}: {str(e)}")
                continue

        # Ensure uniqueness of chart data before returning
        unique_chart_data = self.ensure_unique_chart_data(chart_data)
        return unique_chart_data
    
    @staticmethod
    def is_valid_chart_recommendation(chart_rec: Dict) -> bool:
        """
        Validate chart recommendation format.

        Args:
            chart_rec: The chart recommendation to validate

        Returns:
            True if the recommendation is valid, False otherwise
        """
        return (isinstance(chart_rec, dict) and
                'chart_type' in chart_rec and
                'fields' in chart_rec and
                'chart_group' in chart_rec)

    @staticmethod
    def ensure_unique_chart_data(chart_data_list: List[Dict]) -> List[Dict]:
        """
        Ensure chart data contains unique combinations to avoid repetitive charts.

        Args:
            chart_data_list: List of chart data dictionaries

        Returns:
            Filtered list with unique chart combinations
        """
        seen_combinations = set()
        unique_charts = []

        for chart in chart_data_list:
            try:
                # Create a unique key based on chart type and main field
                chart_type = chart.get('chart_type', '')
                fields = chart.get('fields', {})
                x_field = fields.get('x', '')
                y_field = fields.get('y', '')
                group_field = fields.get('group', '')

                # Create combination key
                combination_key = (chart_type, x_field, y_field, group_field)

                if combination_key not in seen_combinations:
                    seen_combinations.add(combination_key)
                    unique_charts.append(chart)
                    logger.debug(f"Added unique chart: {chart_type} with fields {x_field}, {y_field}, {group_field}")
                else:
                    logger.debug(f"Skipped duplicate chart: {chart_type} with fields {x_field}, {y_field}, {group_field}")

            except Exception as e:
                logger.warning(f"Error checking chart uniqueness: {str(e)}")
                # Include the chart if we can't validate it
                unique_charts.append(chart)

        logger.info(f"Filtered {len(chart_data_list)} charts to {len(unique_charts)} unique charts")
        return unique_charts
