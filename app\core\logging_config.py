# app/core/logging_config.py
import logging
import os
import inspect
from contextvars import <PERSON>textV<PERSON>
from app.core.context import get_request_id

# Environment variable to control verbose logging
VERBOSE_LOGGING = os.environ.get('VERBOSE_LOGGING', 'false').lower() == 'true'

class ContextFilter(logging.Filter):
    def filter(self, record):
        # Add interaction_id from context
        record.interaction_id = get_request_id() or "no-interaction-id"

        # Add filename and function name for better debugging
        if hasattr(record, 'pathname'):
            record.filename = os.path.basename(record.pathname)
        else:
            record.filename = "unknown"

        if hasattr(record, 'funcName'):
            record.function_name = record.funcName
        else:
            record.function_name = "unknown"

        return True

def configure_logging(module_name=None):
    """
    Configure logging with appropriate levels and enhanced formatting.

    Args:
        module_name: Optional module name to get a logger for a specific module

    Returns:
        A configured logger instance
    """
    # Get the logger for the calling module
    logger_name = module_name if module_name else __name__
    logger = logging.getLogger(logger_name)

    # Avoid duplicate handlers
    if logger.handlers:
        return logger

    # Set up the basic configuration with enhanced format
    log_format = (
        '%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(function_name)s() - '
        '%(message)s | interaction_id=%(interaction_id)s'
    )

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO if VERBOSE_LOGGING else logging.WARNING)

    # Create formatter and add it to handler
    formatter = logging.Formatter(log_format)
    console_handler.setFormatter(formatter)

    # Add the context filter to include interaction_id and other context
    context_filter = ContextFilter()
    console_handler.addFilter(context_filter)

    # Add handler to logger
    logger.addHandler(console_handler)
    logger.setLevel(logging.INFO if VERBOSE_LOGGING else logging.WARNING)

    # Set specific log levels for different modules
    if not VERBOSE_LOGGING:
        # Keep data processing logs at WARNING level to reduce verbosity
        logging.getLogger('app.services.data_processor').setLevel(logging.WARNING)
        logging.getLogger('app.services.data_validator').setLevel(logging.WARNING)
        logging.getLogger('app.services.data_analytics').setLevel(logging.WARNING)
        logging.getLogger('app.services.data_insights').setLevel(logging.WARNING)

        # Keep important application logs at INFO level
        logging.getLogger('app.main').setLevel(logging.INFO)
        logging.getLogger('app.api').setLevel(logging.INFO)
        logging.getLogger('app.services.csv_service_v2').setLevel(logging.INFO)

    return logger
