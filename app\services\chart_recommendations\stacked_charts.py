"""
Stacked chart recommendations module.

This module contains functions for recommending stacked charts like stacked line and stacked area.
"""

from typing import List, Dict
from app.core.logging_config import configure_logging
from app.services.chart_recommendations.base import ChartRecommendationBase

logger = configure_logging()

class StackedChartRecommender(ChartRecommendationBase):
    """Class for stacked chart recommendations."""
    
    def get_recommendations(self, metadata: Dict) -> List[Dict]:
        """
        Get stacked chart recommendations based on data characteristics.

        Args:
            metadata: The metadata dictionary containing information about the dataset

        Returns:
            List of stacked chart recommendations
        """
        recommendations = []

        try:
            # Safely get columns from metadata with defaults
            datetime_columns = metadata.get("datetime_columns", [])
            numerical_columns = metadata.get("numerical_columns", [])
            categorical_columns = metadata.get("categorical_columns", [])
            date_metadata = metadata.get("date_metadata", {})
            unique_counts = metadata.get("unique_counts", {})

            # Global used_combinations set to ensure uniqueness across all stacked chart types
            used_combinations = set()

            # Stacked line and area charts for time series with categorical grouping
            if datetime_columns and categorical_columns and numerical_columns:
                # Prioritize categorical columns by unique count for time series stacking
                prioritized_categorical = []
                if categorical_columns:
                    # Get unique counts for each categorical column
                    cat_unique_counts = {col: unique_counts.get(col, 0) for col in categorical_columns}

                    # Filter out columns with only one unique value and limit to reasonable stacking size
                    valid_categorical = [col for col in categorical_columns if 2 <= cat_unique_counts.get(col, 0) <= 8]

                    # Sort by unique count (ascending) to prioritize columns with fewer categories
                    prioritized_categorical = sorted(valid_categorical, key=lambda col: cat_unique_counts.get(col, float('inf')))

                if prioritized_categorical:
                    # Create a list of chart types to generate for time series
                    chart_types_to_generate = ["stacked_line", "stacked_area"]

                    # Generate charts with unique combinations
                    for chart_type in chart_types_to_generate:
                        # Find an unused combination
                        selected_combination = None

                        # Try different combinations of datetime, categorical and numerical columns
                        for date_col in datetime_columns[:2]:  # Consider first 2 datetime columns
                            for cat_col in prioritized_categorical[:3]:  # Consider first 3 categorical columns
                                for num_col in numerical_columns[:3]:  # Consider first 3 numerical columns
                                    combination_key = (date_col, cat_col, num_col)

                                    if combination_key not in used_combinations:
                                        selected_combination = (date_col, cat_col, num_col)
                                        used_combinations.add(combination_key)
                                        break

                                if selected_combination:
                                    break

                            if selected_combination:
                                break

                        # If we found a unique combination, create the chart
                        if selected_combination:
                            date_col, cat_col, num_col = selected_combination
                            recommended_agg = self._get_recommended_time_aggregation(date_metadata)
                            date_format = self._format_datetime_columns(date_col, recommended_agg)

                            recommendations.append({
                                "chart_type": chart_type,
                                "fields": {
                                    "x": date_col,
                                    "x_type": "datetime",
                                    "group": cat_col,
                                    "numeric": num_col,
                                    "time_agg": recommended_agg,
                                    "date_format": date_format,
                                    "chart_title": f"{chart_type.replace('_', ' ').title()}: {num_col.replace('_', ' ').title()} by {cat_col.replace('_', ' ').title()} Over Time"
                                }
                            })
                            logger.info(f"Added time series {chart_type} with unique combination: {date_col} + {cat_col} + {num_col}")

            # Stacked charts for categorical data (non-time series)
            if len(categorical_columns) >= 2 and numerical_columns:
                # Find valid combinations of categorical columns for stacked charts
                valid_stacked_combinations = []

                # Prioritize categorical columns by unique count
                prioritized_categorical = []
                if categorical_columns:
                    # Get unique counts for each categorical column
                    cat_unique_counts = {col: unique_counts.get(col, 0) for col in categorical_columns}

                    # Filter out columns with only one unique value
                    valid_categorical = [col for col in categorical_columns if cat_unique_counts.get(col, 0) > 1]

                    # Sort by unique count (ascending)
                    prioritized_categorical = sorted(valid_categorical, key=lambda col: cat_unique_counts.get(col, float('inf')))

                if len(prioritized_categorical) >= 2:
                    for i, cat_col1 in enumerate(prioritized_categorical[:4]):  # Consider top 4 columns
                        for cat_col2 in prioritized_categorical[i+1:4]:  # Limit to first 4 columns
                            # Get unique counts for both columns
                            unique_count1 = unique_counts.get(cat_col1, 0)
                            unique_count2 = unique_counts.get(cat_col2, 0)

                            # Only consider combinations if both columns have at least 2 unique values
                            # and the combined cardinality is reasonable
                            if unique_count1 >= 2 and unique_count2 >= 2 and unique_count1 <= 12:
                                valid_stacked_combinations.append((cat_col1, cat_col2))

                    # Log the combinations we're using
                    if valid_stacked_combinations:
                        logger.info(f"Valid stacked chart combinations: {valid_stacked_combinations}")

                    # Create a list of chart types to generate
                    chart_types_to_generate = ["stacked_bar", "stacked_line", "stacked_area"]

                    # Generate charts with unique combinations
                    combination_index = 0
                    for chart_type in chart_types_to_generate:
                        # Find an unused combination
                        selected_combination = None
                        selected_num_col = None

                        # Try different combinations of categorical and numerical columns
                        for num_col in numerical_columns[:3]:  # Limit to first 3 numerical columns
                            for combo in valid_stacked_combinations:
                                x_col, group_col = combo
                                combination_key = (x_col, group_col, num_col)

                                if combination_key not in used_combinations:
                                    selected_combination = combo
                                    selected_num_col = num_col
                                    used_combinations.add(combination_key)
                                    break

                            if selected_combination:
                                break

                        # If no unique combination found, use the next available combination
                        if not selected_combination and combination_index < len(valid_stacked_combinations):
                            selected_combination = valid_stacked_combinations[combination_index]
                            selected_num_col = numerical_columns[0] if numerical_columns else "count"
                            combination_index += 1

                        if selected_combination:
                            x_col, group_col = selected_combination

                            # Add the chart with unique combination
                            chart_fields = {
                                "x": x_col,
                                "x_type": "categorical",
                                "group": group_col,
                                "numeric": selected_num_col if selected_num_col != "count" else "count",
                                "chart_title": f"{chart_type.replace('_', ' ').title()}: {selected_num_col} by {x_col} and {group_col}"
                            }

                            # Only add stacked line and area charts if x-axis has reasonable number of points
                            if chart_type in ["stacked_line", "stacked_area"]:
                                if unique_counts.get(x_col, 0) <= 12:  # Limit to reasonable number of x-axis points
                                    recommendations.append({
                                        "chart_type": chart_type,
                                        "fields": chart_fields
                                    })
                                    logger.info(f"Added {chart_type} with unique combination: {x_col} + {group_col} + {selected_num_col}")
                            else:
                                # Always add stacked bar charts
                                recommendations.append({
                                    "chart_type": chart_type,
                                    "fields": chart_fields
                                })
                                logger.info(f"Added {chart_type} with unique combination: {x_col} + {group_col} + {selected_num_col}")

            return recommendations

        except Exception as e:
            logger.error(f"Error in StackedChartRecommender.get_recommendations(): {str(e)}", exc_info=True)
            return []
