"""
Chart Recommender Service.

This module provides chart recommendations based on dataset characteristics.
It uses specialized modules for different chart types to generate recommendations.
"""

from app.core.logging_config import configure_logging
from app.core.config import Settings
import json
from pydantic import BaseModel
from typing import List, Dict, Optional


# Import chart recommendation modules
from app.services.chart_recommendations import (
    HierarchicalChartRecommender,
    StackedChartRecommender,
    MultiChartRecommender,
    NumericalChartRecommender,
    BasicChartRecommender,
    TimeSeriesChartRecommender,
    MultiGroupChartRecommender,
    BoxPlotRecommender,
    HeatmapChartRecommender
)

logger = configure_logging()
settings = Settings()

# Chart type to chart group mapping
CHART_TYPE_TO_GROUP = {
    # Basic charts
    "pie": "basic",
    "doughnut": "basic",
    "semi_circle": "basic",
    "bar": "basic",
    "horizontal_bar": "basic",
    "column": "basic",
    "funnel": "basic",
    "polar_area": "basic",

    # Numerical charts
    "histogram": "numerical",
    "scatter": "numerical",
    "bubble": "numerical",

    # Time series charts
    "line": "time_series",
    "area": "time_series",
    "timeline": "time_series",

    # Box plot charts
    "box": "box_plot",
    "violin": "box_plot",

    # Heatmap charts
    "heatmap": "heatmap",
    "calendar_heatmap": "heatmap",

    # Stacked charts
    "stacked_bar": "stacked",
    "stacked_line": "stacked",
    "stacked_area": "stacked",

    # Multi charts
    "multi_line": "multi",
    "multi_area": "multi",
    "combo_bar_line": "multi",

    # Multi group charts
    "grouped_bar": "multi_group",

    # Hierarchical charts
    "treemap": "hierarchical",
    "sunburst": "hierarchical",
    "icicle": "hierarchical"
}

def get_chart_group(chart_type: str) -> str:
    """
    Get the chart group for a given chart type.

    Args:
        chart_type: The chart type

    Returns:
        The chart group name
    """
    return CHART_TYPE_TO_GROUP.get(chart_type, "basic")

def get_allowed_chart_types_for_group(chart_group: str) -> str:
    """
    Get comma-separated list of allowed chart types for a given chart group.
    This enables UI chart type switching with the same data.

    Args:
        chart_group: The chart group name

    Returns:
        Comma-separated string of chart types in the group
    """
    # Group chart types by their group
    group_to_types = {}
    for chart_type, group in CHART_TYPE_TO_GROUP.items():
        if group not in group_to_types:
            group_to_types[group] = []
        group_to_types[group].append(chart_type)

    # Return comma-separated chart types for the requested group
    chart_types = group_to_types.get(chart_group, [])
    return ",".join(sorted(chart_types))

def get_allowed_chart_types_for_chart_type(chart_type: str) -> str:
    """
    Get comma-separated list of allowed chart types for a given chart type.
    This is a convenience function that gets the group first, then the allowed types.

    Args:
        chart_type: The chart type

    Returns:
        Comma-separated string of chart types in the same group
    """
    chart_group = get_chart_group(chart_type)
    return get_allowed_chart_types_for_group(chart_group)

class ChartField(BaseModel):
    x: Optional[str] = None
    x_type: Optional[str] = None
    y: Optional[str] = None
    y_type: Optional[str] = None
    numeric: Optional[str] = None
    agg: Optional[str] = None
    bin_size: Optional[int] = None
    size: Optional[str] = None
    group: Optional[str] = None
    group_type: Optional[str] = "categorical"
    chart_title: Optional[str] = None
    x_axis_title: Optional[str] = None
    y_axis_title: Optional[str] = None

class ChartRecommendation(BaseModel):
    chart_type: str
    fields: ChartField

class ChartRecommendationsResponse(BaseModel):
    chart_types: List[ChartRecommendation]

class ChartData:
    def __init__(self):

        # Initialize chart recommenders
        self.hierarchical_recommender = HierarchicalChartRecommender()
        self.stacked_recommender = StackedChartRecommender()
        self.multi_recommender = MultiChartRecommender()
        self.numerical_recommender = NumericalChartRecommender()
        self.basic_recommender = BasicChartRecommender()
        self.time_series_recommender = TimeSeriesChartRecommender()
        self.multi_group_recommender = MultiGroupChartRecommender()
        self.box_plot_recommender = BoxPlotRecommender()
        self.heatmap_recommender = HeatmapChartRecommender()



    def get_rule_based_recommendations(self, metadata: Dict) -> List[Dict]:
        """Get chart recommendations based on data characteristics using rule-based logic with configurable limits."""
        from app.core.config import settings

        # Log metadata summary for debugging
        logger.debug("=== CHART RECOMMENDATION DEBUG START ===")
        logger.debug(f"Dataset metadata summary:")
        logger.debug(f"  - Categorical columns: {len(metadata.get('categorical_columns', []))}")
        logger.debug(f"  - Numerical columns: {len(metadata.get('numerical_columns', []))}")
        logger.debug(f"  - Total rows: {metadata.get('total_rows', 'unknown')}")
        logger.debug(f"  - Unique counts: {metadata.get('unique_counts', {})}")

        recommendations = []

        # Get recommendations from each specialized recommender with error handling and configurable limits
        recommender_configs = [
            ("BasicChartRecommender", self.basic_recommender.get_recommendations, settings.MAX_BASIC_CHART_RECOMMENDATIONS),
            ("NumericalChartRecommender", self.numerical_recommender.get_recommendations, settings.MAX_NUMERICAL_CHART_RECOMMENDATIONS),
            ("TimeSeriesChartRecommender", self.time_series_recommender.get_recommendations, settings.MAX_TIME_SERIES_CHART_RECOMMENDATIONS),
            ("BoxPlotRecommender", self.box_plot_recommender.get_recommendations, settings.MAX_BOX_PLOT_CHART_RECOMMENDATIONS),
            ("HeatmapChartRecommender", self.heatmap_recommender.get_recommendations, settings.MAX_HEATMAP_CHART_RECOMMENDATIONS),
            ("StackedChartRecommender", self.stacked_recommender.get_recommendations, settings.MAX_STACKED_CHART_RECOMMENDATIONS),
            ("MultiChartRecommender", self.multi_recommender.get_recommendations, settings.MAX_MULTI_CHART_RECOMMENDATIONS),
            ("MultiGroupChartRecommender", self.multi_group_recommender.get_recommendations, settings.MAX_MULTI_GROUP_CHART_RECOMMENDATIONS),
            ("HierarchicalChartRecommender", self.hierarchical_recommender.get_recommendations, settings.MAX_HIERARCHICAL_CHART_RECOMMENDATIONS)
        ]

        for recommender_name, recommender_method, max_limit in recommender_configs:
            try:
                logger.debug(f"--- Processing {recommender_name} ---")
                recs = recommender_method(metadata)
                if recs:
                    # Apply limit before adding to recommendations
                    limited_recs = recs[:max_limit]
                    recommendations.extend(limited_recs)
                    logger.debug(f"{recommender_name} generated {len(limited_recs)}/{len(recs)} recommendations (limit: {max_limit})")

                    # Log details of each recommendation for debugging
                    for i, rec in enumerate(limited_recs):
                        logger.debug(f"  [{i+1}] {rec.get('chart_type', 'unknown')} - "
                                   f"X: {rec.get('x_axis', 'N/A')}, Y: {rec.get('y_axis', 'N/A')}, "
                                   f"Score: {rec.get('suitability_score', 'N/A')}")
                else:
                    logger.debug(f"{recommender_name} generated 0 recommendations")
            except Exception as e:
                logger.error(f"Error in {recommender_name}.get_recommendations(): {str(e)}", exc_info=True)
                # Continue with other recommenders even if one fails

        # Add chart_group field to each recommendation
        for recommendation in recommendations:
            chart_type = recommendation.get('chart_type')
            if chart_type:
                recommendation['chart_group'] = get_chart_group(chart_type)

        # Apply overall limit to total recommendations
        max_total_recommendations = settings.MAX_CHART_RECOMMENDATIONS
        if len(recommendations) > max_total_recommendations:
            recommendations = recommendations[:max_total_recommendations]
            logger.info(f"Applied overall limit: {max_total_recommendations} recommendations")

        # Debug summary
        logger.debug("=== CHART RECOMMENDATION DEBUG SUMMARY ===")
        logger.debug(f"Total recommendations generated: {len(recommendations)}")

        # Group recommendations by chart type for summary
        chart_type_counts = {}
        for rec in recommendations:
            chart_type = rec.get('chart_type', 'unknown')
            chart_type_counts[chart_type] = chart_type_counts.get(chart_type, 0) + 1

        logger.debug("Recommendations by chart type:")
        for chart_type, count in sorted(chart_type_counts.items()):
            logger.debug(f"  - {chart_type}: {count}")

        logger.debug("=== CHART RECOMMENDATION DEBUG END ===")
        logger.info(f"Generated {len(recommendations)} total chart recommendations")
        return recommendations
    

   

